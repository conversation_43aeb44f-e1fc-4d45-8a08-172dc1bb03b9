{% extends 'base.html' %}

{% block title %}Perfil - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Meu Perfil</h1>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Informações Pessoais</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Nome:</strong></td>
                        <td>{{ user.get_full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>Usuário:</strong></td>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>{{ user.email }}</td>
                    </tr>
                    <tr>
                        <td><strong>Telefone:</strong></td>
                        <td>{{ user.phone|default:"Não informado" }}</td>
                    </tr>
                    <tr>
                        <td><strong>Tipo de Usuário:</strong></td>
                        <td>
                            <span class="badge bg-primary">{{ user.get_user_type_display }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Membro desde:</strong></td>
                        <td>{{ user.date_joined|date:"d/m/Y" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Estatísticas</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Suas estatísticas no sistema aparecerão aqui.</p>
                <!-- Aqui você pode adicionar estatísticas específicas do usuário -->
            </div>
        </div>
    </div>
</div>
{% endblock %}
