from django.db import models
from django.conf import settings
from crm.models import Client
from decimal import Decimal

class Proposal(models.Model):
    STATUS_CHOICES = (
        ('draft', '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
        ('sent', 'Enviada'),
        ('accepted', '<PERSON><PERSON>'),
        ('rejected', 'Recusada'),
        ('expired', 'Expirada'),
    )
    
    title = models.CharField(max_length=200, verbose_name='Título')
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='proposals',
        verbose_name='Cliente'
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='Criado por'
    )
    description = models.TextField(verbose_name='Descrição')
    value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Valor'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name='Status'
    )
    valid_until = models.DateField(verbose_name='<PERSON><PERSON>lid<PERSON> até')
    terms = models.TextField(
        blank=True,
        verbose_name='Termos e Condições'
    )
    notes = models.TextField(
        blank=True,
        verbose_name='Observações Internas'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Atualizado em')
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name='Enviado em')
    
    class Meta:
        verbose_name = 'Proposta'
        verbose_name_plural = 'Propostas'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.client.name}"
    
    def get_status_color(self):
        colors = {
            'draft': '#6c757d',
            'sent': '#007bff',
            'accepted': '#28a745',
            'rejected': '#dc3545',
            'expired': '#fd7e14',
        }
        return colors.get(self.status, '#6c757d')

class ProposalItem(models.Model):
    proposal = models.ForeignKey(
        Proposal,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='Proposta'
    )
    description = models.CharField(max_length=200, verbose_name='Descrição')
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1,
        verbose_name='Quantidade'
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Preço Unitário'
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Preço Total'
    )
    
    class Meta:
        verbose_name = 'Item da Proposta'
        verbose_name_plural = 'Itens da Proposta'
    
    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.description} - {self.proposal.title}"
