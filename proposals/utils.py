from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from io import BytesIO
from django.utils import timezone

def generate_proposal_pdf(proposal):
    """
    Gera um PDF da proposta usando ReportLab
    """
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Título
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    story.append(Paragraph(f"PROPOSTA COMERCIAL", title_style))
    story.append(Spacer(1, 12))
    
    # Informações da proposta
    info_style = styles['Normal']
    story.append(Paragraph(f"<b>Proposta:</b> {proposal.title}", info_style))
    story.append(Paragraph(f"<b>Cliente:</b> {proposal.client.name}", info_style))
    if proposal.client.company:
        story.append(Paragraph(f"<b>Empresa:</b> {proposal.client.company}", info_style))
    story.append(Paragraph(f"<b>Email:</b> {proposal.client.email}", info_style))
    story.append(Paragraph(f"<b>Telefone:</b> {proposal.client.phone}", info_style))
    story.append(Paragraph(f"<b>Data:</b> {timezone.now().strftime('%d/%m/%Y')}", info_style))
    story.append(Paragraph(f"<b>Válida até:</b> {proposal.valid_until.strftime('%d/%m/%Y')}", info_style))
    story.append(Spacer(1, 20))
    
    # Descrição
    story.append(Paragraph("<b>Descrição:</b>", styles['Heading2']))
    story.append(Paragraph(proposal.description, info_style))
    story.append(Spacer(1, 20))
    
    # Itens da proposta
    if proposal.items.exists():
        story.append(Paragraph("<b>Itens da Proposta:</b>", styles['Heading2']))
        
        # Cabeçalho da tabela
        data = [['Descrição', 'Qtd', 'Valor Unit.', 'Total']]
        
        # Itens
        for item in proposal.items.all():
            data.append([
                item.description,
                str(item.quantity),
                f"R$ {item.unit_price:,.2f}",
                f"R$ {item.total_price:,.2f}"
            ])
        
        # Total
        data.append(['', '', 'TOTAL:', f"R$ {proposal.value:,.2f}"])
        
        # Criar tabela
        table = Table(data, colWidths=[3*inch, 0.8*inch, 1.2*inch, 1.2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -2), colors.beige),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    # Termos e condições
    if proposal.terms:
        story.append(Paragraph("<b>Termos e Condições:</b>", styles['Heading2']))
        story.append(Paragraph(proposal.terms, info_style))
        story.append(Spacer(1, 20))
    
    # Rodapé
    story.append(Spacer(1, 30))
    story.append(Paragraph("Atenciosamente,", info_style))
    story.append(Spacer(1, 20))
    story.append(Paragraph(f"{proposal.created_by.get_full_name()}", info_style))
    story.append(Paragraph(f"{proposal.created_by.email}", info_style))
    
    # Gerar PDF
    doc.build(story)
    pdf = buffer.getvalue()
    buffer.close()
    
    return pdf
