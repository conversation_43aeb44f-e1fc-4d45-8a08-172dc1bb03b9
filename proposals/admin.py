from django.contrib import admin
from .models import Proposal, ProposalItem

class ProposalItemInline(admin.TabularInline):
    model = ProposalItem
    extra = 1

@admin.register(Proposal)
class ProposalAdmin(admin.ModelAdmin):
    list_display = ('title', 'client', 'value', 'status', 'created_by', 'valid_until', 'created_at')
    list_filter = ('status', 'created_by', 'valid_until', 'created_at')
    search_fields = ('title', 'client__name', 'description')
    list_editable = ('status',)
    date_hierarchy = 'created_at'
    inlines = [ProposalItemInline]
    
    def save_model(self, request, obj, form, change):
        if not change:  # Se é um novo objeto
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(ProposalItem)
class ProposalItemAdmin(admin.ModelAdmin):
    list_display = ('description', 'proposal', 'quantity', 'unit_price', 'total_price')
    list_filter = ('proposal',)
    search_fields = ('description', 'proposal__title')
