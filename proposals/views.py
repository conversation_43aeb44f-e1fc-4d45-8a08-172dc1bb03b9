from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Sum
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse
from django.utils import timezone
from .models import Proposal, ProposalItem
from .forms import ProposalForm, ProposalItemFormSet
from .utils import generate_proposal_pdf

class ProposalListView(LoginRequiredMixin, ListView):
    model = Proposal
    template_name = 'proposals/proposal_list.html'
    context_object_name = 'proposals'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Proposal.objects.select_related('client', 'created_by')
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')
        
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(client__name__icontains=search) |
                Q(description__icontains=search)
            )
        
        if status:
            queryset = queryset.filter(status=status)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Proposal.STATUS_CHOICES
        context['current_status'] = self.request.GET.get('status', '')
        context['current_search'] = self.request.GET.get('search', '')
        
        # Estatísticas
        context['total_proposals'] = Proposal.objects.count()
        context['total_value'] = Proposal.objects.aggregate(
            total=Sum('value')
        )['total'] or 0
        context['accepted_value'] = Proposal.objects.filter(
            status='accepted'
        ).aggregate(total=Sum('value'))['total'] or 0
        
        return context

class ProposalCreateView(LoginRequiredMixin, CreateView):
    model = Proposal
    form_class = ProposalForm
    template_name = 'proposals/proposal_form.html'
    success_url = reverse_lazy('proposals:proposal_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['items_formset'] = ProposalItemFormSet(self.request.POST)
        else:
            context['items_formset'] = ProposalItemFormSet()
        return context
    
    def form_valid(self, form):
        context = self.get_context_data()
        items_formset = context['items_formset']
        
        if items_formset.is_valid():
            form.instance.created_by = self.request.user
            self.object = form.save()
            items_formset.instance = self.object
            items_formset.save()
            
            # Calcular valor total
            total_value = sum(item.total_price for item in self.object.items.all())
            self.object.value = total_value
            self.object.save()
            
            messages.success(self.request, 'Proposta criada com sucesso!')
            return redirect(self.success_url)
        else:
            return self.render_to_response(self.get_context_data(form=form))

class ProposalUpdateView(LoginRequiredMixin, UpdateView):
    model = Proposal
    form_class = ProposalForm
    template_name = 'proposals/proposal_form.html'
    success_url = reverse_lazy('proposals:proposal_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['items_formset'] = ProposalItemFormSet(
                self.request.POST, instance=self.object
            )
        else:
            context['items_formset'] = ProposalItemFormSet(instance=self.object)
        return context
    
    def form_valid(self, form):
        context = self.get_context_data()
        items_formset = context['items_formset']
        
        if items_formset.is_valid():
            self.object = form.save()
            items_formset.instance = self.object
            items_formset.save()
            
            # Calcular valor total
            total_value = sum(item.total_price for item in self.object.items.all())
            self.object.value = total_value
            self.object.save()
            
            messages.success(self.request, 'Proposta atualizada com sucesso!')
            return redirect(self.success_url)
        else:
            return self.render_to_response(self.get_context_data(form=form))

class ProposalDeleteView(LoginRequiredMixin, DeleteView):
    model = Proposal
    template_name = 'proposals/proposal_confirm_delete.html'
    success_url = reverse_lazy('proposals:proposal_list')
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Proposta excluída com sucesso!')
        return super().delete(request, *args, **kwargs)

@login_required
def proposal_detail(request, pk):
    proposal = get_object_or_404(Proposal, pk=pk)
    items = proposal.items.all()
    
    context = {
        'proposal': proposal,
        'items': items,
    }
    
    return render(request, 'proposals/proposal_detail.html', context)

@login_required
def send_proposal(request, pk):
    proposal = get_object_or_404(Proposal, pk=pk)
    
    if proposal.status == 'draft':
        proposal.status = 'sent'
        proposal.sent_at = timezone.now()
        proposal.save()
        messages.success(request, 'Proposta marcada como enviada!')
    else:
        messages.warning(request, 'Esta proposta já foi enviada.')
    
    return redirect('proposals:proposal_detail', pk=pk)

@login_required
def generate_pdf(request, pk):
    proposal = get_object_or_404(Proposal, pk=pk)
    
    try:
        pdf_content = generate_proposal_pdf(proposal)
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="proposta_{proposal.id}.pdf"'
        return response
    except Exception as e:
        messages.error(request, f'Erro ao gerar PDF: {str(e)}')
        return redirect('proposals:proposal_detail', pk=pk)
